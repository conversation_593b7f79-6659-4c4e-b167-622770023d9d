<mxfile host="65bd71144e">
    <diagram id="7LsXEMpDmxM-12nJ1Qir" name="第 1 页">
        <mxGraphModel dx="876" dy="665" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3300" pageHeight="4681" background="none" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="1180" y="460" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="主程序(main.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="1180" y="510" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="DS402状态机&lt;br&gt;(ds402.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="1180" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="电机通信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=center;" parent="1" vertex="1">
                    <mxGeometry x="960" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="电机控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="1400" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="CAN通信&lt;br&gt;(can.c, canopen.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="880" y="700" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="串口通信&lt;br&gt;(usart.c, modbus.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="1040" y="700" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="低级驱动&lt;br&gt;(low_level.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="1300" y="700" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="FOC算法实现&lt;br&gt;(mcpwm.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="1460" y="700" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="位置环控制&lt;br&gt;(position_loop.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="1380" y="800" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="速度环控制&lt;br&gt;(velocity_loop.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="1380" y="880" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="电流环控制&lt;br&gt;(current_loop.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="1380" y="960" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="PWM输出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
                    <mxGeometry x="1300" y="1040" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="反馈接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
                    <mxGeometry x="1460" y="1040" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="参数配置&lt;br&gt;(parameter.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="1160" y="700" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="字典对象&lt;br&gt;(Dic.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="1160" y="800" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="辅助函数&lt;br&gt;(utils.c)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="1040" y="880" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="外设初始化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
                    <mxGeometry x="1160" y="880" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="3" target="4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="3" target="5" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1120" y="540"/>
                            <mxPoint x="1120" y="630"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="3" target="6" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1360" y="540"/>
                            <mxPoint x="1360" y="630"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="5" target="7" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1020" y="680"/>
                            <mxPoint x="940" y="680"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="5" target="8" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1020" y="680"/>
                            <mxPoint x="1100" y="680"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="6" target="9" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1460" y="680"/>
                            <mxPoint x="1360" y="680"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="6" target="10" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1460" y="680"/>
                            <mxPoint x="1520" y="680"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="4" target="16" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="16" target="17" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="17" target="19" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="" style="endArrow=classic;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="17" target="18" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1190" y="910"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="10" target="11" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1520" y="780"/>
                            <mxPoint x="1440" y="780"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="11" target="12" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="940" as="sourcePoint"/>
                        <mxPoint x="1250" y="890" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="12" target="13" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="940" as="sourcePoint"/>
                        <mxPoint x="1250" y="890" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="34" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="13" target="14" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="940" as="sourcePoint"/>
                        <mxPoint x="1250" y="890" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1440" y="1030"/>
                            <mxPoint x="1360" y="1030"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="13" target="15" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="940" as="sourcePoint"/>
                        <mxPoint x="1250" y="890" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1440" y="1030"/>
                            <mxPoint x="1520" y="1030"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" parent="1" source="8" target="16" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" parent="1" source="9" target="16" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" parent="1" source="15" target="13" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="940" as="sourcePoint"/>
                        <mxPoint x="1250" y="890" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1520" y="990"/>
                            <mxPoint x="1520" y="990"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="39" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" parent="1" source="7" target="4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1200" y="840" as="sourcePoint"/>
                        <mxPoint x="1250" y="790" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="860" y="730"/>
                            <mxPoint x="860" y="630"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="图例" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="880" y="1120" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="880" y="1150" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="主程序" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="920" y="1150" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="880" y="1180" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="状态管理" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="920" y="1180" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="880" y="1210" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="通信模块" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="920" y="1210" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="1040" y="1150" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="电机控制模块" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1080" y="1150" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="49" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="1040" y="1180" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="控制算法" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1080" y="1180" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="1040" y="1210" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="配置与工具" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1080" y="1210" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
                    <mxGeometry x="880" y="1240" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="硬件接口" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="920" y="1240" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="55" value="" style="endArrow=classic;html=1;" parent="1" vertex="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1040" y="1250" as="sourcePoint"/>
                        <mxPoint x="1080" y="1250" as="targetPoint"/>
                        <mxPoint x="840" y="420" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="56" value="调用关系" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1080" y="1240" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="" style="endArrow=classic;html=1;dashed=1;" parent="1" vertex="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1040" y="1270" as="sourcePoint"/>
                        <mxPoint x="1080" y="1270" as="targetPoint"/>
                        <mxPoint x="840" y="420" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="58" value="反馈或间接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="1080" y="1260" width="120" height="20" as="geometry"/>
                </mxCell>
                <object label="&lt;div style=&quot;color: rgb(204, 204, 204); background-color: rgb(31, 31, 31); font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 16px; line-height: 22px; white-space: pre;&quot;&gt;&lt;span style=&quot;color: rgb(204, 204, 204);&quot;&gt;&amp;nbsp; &amp;nbsp; &lt;/span&gt;&lt;span style=&quot;color: rgb(220, 220, 170);&quot;&gt;Process_Store_parameter&lt;/span&gt;&lt;span style=&quot;color: rgb(204, 204, 204);&quot;&gt;()&lt;/span&gt;&lt;/div&gt;" hedietLinkedDataV1_path="../Core/Src/parameter.c" hedietLinkedDataV1_start_col_x-num="28" hedietLinkedDataV1_start_line_x-num="261" hedietLinkedDataV1_end_col_x-num="28" hedietLinkedDataV1_end_line_x-num="261" id="59">
                    <mxCell style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                        <mxGeometry x="1850" y="530" width="280" height="70" as="geometry"/>
                    </mxCell>
                </object>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>